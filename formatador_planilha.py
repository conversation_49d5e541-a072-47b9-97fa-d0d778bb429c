#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Formatador de Planilha
Aplica regras específicas de formatação para contatos em planilhas Excel/CSV
"""

import pandas as pd
import re
import os
from typing import Dict, List, Tuple, Optional


class FormatadorPlanilha:
    """Classe para formatar planilhas seguindo regras específicas"""
    
    def __init__(self):
        self.estados_validos = {
            'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 
            'MA', 'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 
            'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'
        }
    
    def limpar_numeros(self, texto: str) -> str:
        """Remove tudo que não for número"""
        if pd.isna(texto) or texto == '':
            return ''
        return re.sub(r'[^0-9]', '', str(texto))
    
    def formatar_telefone(self, telefone: str) -> <PERSON><PERSON>[str, List[str]]:
        """
        Formata telefone seguindo as regras:
        - Apenas 1 número válido
        - 10 ou 11 dígitos
        - Se faltar dígitos, acrescentar 0 no início
        - Contatos extras vão para anotações
        """
        if pd.isna(telefone) or telefone == '':
            return '', []
        
        # Separar múltiplos números (por vírgula, ponto e vírgula, barra, etc.)
        numeros = re.split(r'[,;/\|\n\r]+', str(telefone))
        numeros_limpos = []
        numeros_extras = []
        
        for num in numeros:
            num_limpo = self.limpar_numeros(num.strip())
            if num_limpo:
                numeros_limpos.append(num_limpo)
        
        if not numeros_limpos:
            return '', []
        
        # Pegar o primeiro número válido
        numero_principal = numeros_limpos[0]
        
        # Validar e formatar o número principal
        if len(numero_principal) < 10:
            # Completar com zeros no início
            numero_principal = numero_principal.zfill(11)  # Preferir 11 dígitos
        elif len(numero_principal) > 11:
            # Número muito longo, considerar inválido
            numeros_extras.append(f"Número inválido: {telefone}")
            numero_principal = ''
        
        # Números extras vão para anotações
        for i, num in enumerate(numeros_limpos[1:], 1):
            if len(num) >= 10 and len(num) <= 11:
                if len(num) == 10:
                    num = num.zfill(11)
                numeros_extras.append(f"Contato extra {i}: {num}")
            else:
                numeros_extras.append(f"Contato inválido {i}: {numeros[i]}")
        
        return numero_principal, numeros_extras
    
    def formatar_cep(self, cep: str) -> str:
        """
        Formatar CEP:
        - 8 dígitos numéricos
        - Se tiver menos, completar com 0 no final
        """
        if pd.isna(cep) or cep == '':
            return ''
        
        cep_limpo = self.limpar_numeros(str(cep))
        
        if len(cep_limpo) == 0:
            return ''
        elif len(cep_limpo) < 8:
            # Completar com zeros no final
            cep_limpo = cep_limpo.ljust(8, '0')
        elif len(cep_limpo) > 8:
            # Pegar apenas os primeiros 8 dígitos
            cep_limpo = cep_limpo[:8]
        
        # Formatar como 00000-000
        return f"{cep_limpo[:5]}-{cep_limpo[5:]}"
    
    def formatar_estado(self, estado: str) -> Tuple[str, List[str]]:
        """
        Formatar estado:
        - Apenas sigla UF válida
        - Texto extra vai para anotações
        """
        if pd.isna(estado) or estado == '':
            return '', []
        
        estado_str = str(estado).strip().upper()
        anotacoes = []
        
        # Procurar por sigla válida no texto
        for uf in self.estados_validos:
            if uf in estado_str:
                if estado_str != uf:
                    anotacoes.append(f"Estado original: {estado}")
                return uf, anotacoes
        
        # Se não encontrou sigla válida
        anotacoes.append(f"Estado inválido: {estado}")
        return '', anotacoes
    
    def processar_linha(self, linha: pd.Series) -> pd.Series:
        """Processa uma linha da planilha aplicando todas as regras"""
        resultado = linha.copy()
        anotacoes_extras = []
        
        # 1. Nome - manter como está
        # (não precisa processar)
        
        # 2. Celular
        if 'Celular' in linha.index or 'celular' in linha.index:
            col_celular = 'Celular' if 'Celular' in linha.index else 'celular'
            celular_formatado, extras_celular = self.formatar_telefone(linha[col_celular])
            resultado[col_celular] = celular_formatado
            anotacoes_extras.extend(extras_celular)
        
        # 3. Telefone
        if 'Telefone' in linha.index or 'telefone' in linha.index:
            col_telefone = 'Telefone' if 'Telefone' in linha.index else 'telefone'
            telefone_formatado, extras_telefone = self.formatar_telefone(linha[col_telefone])
            resultado[col_telefone] = telefone_formatado
            anotacoes_extras.extend(extras_telefone)
        
        # 4. País - manter como está
        # (não precisa processar)
        
        # 5. Estado
        if 'Estado' in linha.index or 'estado' in linha.index:
            col_estado = 'Estado' if 'Estado' in linha.index else 'estado'
            estado_formatado, extras_estado = self.formatar_estado(linha[col_estado])
            resultado[col_estado] = estado_formatado
            anotacoes_extras.extend(extras_estado)
        
        # 6-8. Cidade, Bairro, Endereço - manter como estão
        # (não precisam processar)
        
        # 9. CEP
        if 'CEP' in linha.index or 'cep' in linha.index:
            col_cep = 'CEP' if 'CEP' in linha.index else 'cep'
            resultado[col_cep] = self.formatar_cep(linha[col_cep])
        
        # 10. Anotações Gerais
        col_anotacoes = None
        for col in ['Anotações Gerais', 'anotacoes_gerais', 'Anotacoes', 'Observações', 'observacoes']:
            if col in linha.index:
                col_anotacoes = col
                break
        
        if col_anotacoes is None:
            # Criar coluna de anotações se não existir
            col_anotacoes = 'Anotações Gerais'
            resultado[col_anotacoes] = ''
        
        # Adicionar anotações existentes
        anotacoes_atuais = str(resultado[col_anotacoes]) if not pd.isna(resultado[col_anotacoes]) else ''
        
        # Combinar com novas anotações
        if anotacoes_extras:
            if anotacoes_atuais and anotacoes_atuais != 'nan':
                todas_anotacoes = [anotacoes_atuais] + anotacoes_extras
            else:
                todas_anotacoes = anotacoes_extras
            resultado[col_anotacoes] = ' | '.join(todas_anotacoes)
        elif not anotacoes_atuais or anotacoes_atuais == 'nan':
            resultado[col_anotacoes] = ''
        
        return resultado
    
    def processar_planilha(self, arquivo_entrada: str, arquivo_saida: str = None) -> str:
        """
        Processa uma planilha completa
        
        Args:
            arquivo_entrada: Caminho para o arquivo de entrada (Excel ou CSV)
            arquivo_saida: Caminho para o arquivo de saída (opcional)
        
        Returns:
            Caminho do arquivo de saída
        """
        # Determinar arquivo de saída
        if arquivo_saida is None:
            nome_base = os.path.splitext(arquivo_entrada)[0]
            extensao = os.path.splitext(arquivo_entrada)[1]
            arquivo_saida = f"{nome_base}_formatado{extensao}"
        
        # Ler arquivo
        try:
            if arquivo_entrada.endswith('.csv'):
                df = pd.read_csv(arquivo_entrada, encoding='utf-8')
            else:
                df = pd.read_excel(arquivo_entrada)
        except UnicodeDecodeError:
            # Tentar com encoding diferente
            if arquivo_entrada.endswith('.csv'):
                df = pd.read_csv(arquivo_entrada, encoding='latin-1')
            else:
                df = pd.read_excel(arquivo_entrada)
        
        print(f"Processando {len(df)} linhas...")
        
        # Processar cada linha
        df_formatado = df.apply(self.processar_linha, axis=1)
        
        # Salvar arquivo
        if arquivo_saida.endswith('.csv'):
            df_formatado.to_csv(arquivo_saida, index=False, encoding='utf-8')
        else:
            df_formatado.to_excel(arquivo_saida, index=False)
        
        print(f"Arquivo formatado salvo em: {arquivo_saida}")
        return arquivo_saida


def main():
    """Função principal para uso via linha de comando"""
    import sys
    
    if len(sys.argv) < 2:
        print("Uso: python formatador_planilha.py <arquivo_entrada> [arquivo_saida]")
        print("Exemplo: python formatador_planilha.py contatos.xlsx")
        return
    
    arquivo_entrada = sys.argv[1]
    arquivo_saida = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not os.path.exists(arquivo_entrada):
        print(f"Erro: Arquivo '{arquivo_entrada}' não encontrado.")
        return
    
    formatador = FormatadorPlanilha()
    try:
        arquivo_final = formatador.processar_planilha(arquivo_entrada, arquivo_saida)
        print(f"✅ Formatação concluída! Arquivo salvo em: {arquivo_final}")
    except Exception as e:
        print(f"❌ Erro ao processar planilha: {e}")


if __name__ == "__main__":
    main()
