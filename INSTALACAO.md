# 🚀 Guia de Instalação - Formatador de Planilha

## 📋 Pré-requisitos

- Python 3.7 ou superior
- Windows, macOS ou Linux

## 🔧 Instalação

### 1. Verificar Python

Abra o terminal/prompt de comando e digite:

```bash
python --version
```

Se não tiver Python instalado, baixe em: https://python.org

### 2. Instalar Dependências

No diretório do projeto, execute:

```bash
pip install -r requirements.txt
```

Ou instale manualmente:

```bash
pip install pandas openpyxl
```

## 🎯 Uso Rápido

### Método 1: Script Simples (Recomendado)

```bash
# Formatar uma planilha
python formatar.py sua_planilha.xlsx
```

### Método 2: Linha de Comando Completa

```bash
# Usar o formatador principal
python formatador_planilha.py sua_planilha.xlsx

# Especificar arquivo de saída
python formatador_planilha.py entrada.xlsx saida.xlsx
```

### Método 3: Demonstração

```bash
# Ver exemplo completo funcionando
python exemplo_uso.py
```

## 📁 Estrutura de Arquivos

Após a instalação, você terá:

```
formatplanilha/
├── formatar.py              # ⭐ Script principal (mais fácil)
├── formatador_planilha.py   # Classe completa
├── exemplo_uso.py           # Demonstração
├── README.md               # Documentação completa
├── INSTALACAO.md           # Este arquivo
└── requirements.txt        # Dependências
```

## 🧪 Teste de Funcionamento

1. Execute a demonstração:
```bash
python exemplo_uso.py
```

2. Isso criará arquivos de exemplo e mostrará o antes/depois

3. Se funcionou, está tudo pronto! 🎉

## ❓ Problemas Comuns

### "python não é reconhecido"
- Instale Python de python.org
- Marque "Add to PATH" durante instalação

### "pip não é reconhecido"
- Reinstale Python marcando "Add to PATH"
- Ou use: `python -m pip install pandas openpyxl`

### "Permission denied"
- Feche arquivos Excel que estejam abertos
- Execute como administrador se necessário

### Erro de encoding (CSV)
- Salve o CSV como UTF-8
- Ou use Excel (.xlsx) que é mais confiável

## 🎯 Próximos Passos

1. **Teste com seus dados**: Faça backup e teste com uma planilha pequena primeiro
2. **Revise resultados**: Sempre confira a coluna "Anotações Gerais"
3. **Use regularmente**: O script é seguro e preserva dados originais

## 📞 Suporte

Se tiver problemas:

1. Verifique se seguiu todos os passos de instalação
2. Teste com o arquivo de exemplo primeiro
3. Verifique se o arquivo não está aberto em outro programa
4. Para CSVs, certifique-se que está em UTF-8

## 🔄 Atualização

Para atualizar as dependências:

```bash
pip install --upgrade pandas openpyxl
```
