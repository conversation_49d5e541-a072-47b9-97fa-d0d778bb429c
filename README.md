# 📋 Formatador de Planilha

Sistema para formatação automática de planilhas de contatos seguindo regras específicas.

## 🎯 Funcionalidades

O formatador aplica as seguintes regras automaticamente:

### 1. **Nome**
- Campo livre (mantido como está)

### 2. **Celular**
- Apenas 1 número válido mantido
- Aceita 10 ou 11 dígitos (com ou sem máscara)
- Se faltarem dígitos, acrescenta 0 no início
- Remove letras e caracteres especiais
- Contatos extras movidos para "Anotações Gerais"

### 3. **Telefone (fixo)**
- Mesmas regras do Celular

### 4. **País**
- Campo livre (mantido como está)

### 5. **Estado**
- Apenas sigla UF válida (SP, RJ, MG, etc.)
- Texto extra movido para "Anotações Gerais"

### 6. **Cidade**
- Campo livre (mantido como está)

### 7. **Bairro**
- Campo livre (mantido como está)

### 8. **Endereço**
- Campo livre (mantido como está)

### 9. **CEP**
- Deve ter 8 dígitos numéricos
- Formato: 00000-000 ou 00000000
- Se tiver menos de 8 dígitos, completa com 0 no final
- Se tiver mais, trunca para 8 dígitos

### 10. **Anotações Gerais**
- Recebe contatos extras
- Recebe contatos inválidos
- Recebe observações que não se encaixam nos outros campos

## 🚀 Como Usar

### Instalação das Dependências

```bash
pip install pandas openpyxl
```

### Uso via Linha de Comando

```bash
# Processar uma planilha
python formatador_planilha.py arquivo_entrada.xlsx

# Especificar arquivo de saída
python formatador_planilha.py arquivo_entrada.xlsx arquivo_saida.xlsx

# Funciona com CSV também
python formatador_planilha.py contatos.csv contatos_formatados.csv
```

### Uso Programático

```python
from formatador_planilha import FormatadorPlanilha

# Criar instância do formatador
formatador = FormatadorPlanilha()

# Processar planilha
arquivo_saida = formatador.processar_planilha('contatos.xlsx')
print(f"Planilha formatada salva em: {arquivo_saida}")
```

### Exemplo Completo

```bash
# Executar demonstração com dados de exemplo
python exemplo_uso.py
```

## 📁 Estrutura dos Arquivos

```
formatplanilha/
├── formatador_planilha.py  # Classe principal do formatador
├── exemplo_uso.py          # Demonstração e testes
├── README.md              # Este arquivo
└── requirements.txt       # Dependências
```

## 🧪 Exemplos de Transformação

### Antes da Formatação:
```
Nome: João Silva
Celular: (11) 99999-8888, 11987654321
Estado: São Paulo - SP
CEP: 1234567
```

### Depois da Formatação:
```
Nome: João Silva
Celular: 01199999888
Estado: SP
CEP: 12345-670
Anotações Gerais: Estado original: São Paulo - SP | Contato extra 1: 01198765432
```

## 🔧 Funcionalidades Técnicas

- **Suporte a múltiplos formatos**: Excel (.xlsx, .xls) e CSV
- **Encoding automático**: Detecta UTF-8 e Latin-1
- **Validação robusta**: Trata dados malformados graciosamente
- **Preservação de dados**: Informações extras vão para anotações
- **Flexibilidade de colunas**: Funciona com diferentes nomes de colunas

## 📋 Colunas Suportadas

O formatador reconhece automaticamente estas variações de nomes:

- **Celular**: `Celular`, `celular`
- **Telefone**: `Telefone`, `telefone`
- **Estado**: `Estado`, `estado`
- **CEP**: `CEP`, `cep`
- **Anotações**: `Anotações Gerais`, `anotacoes_gerais`, `Anotacoes`, `Observações`, `observacoes`

## ⚠️ Observações Importantes

1. **Backup**: Sempre faça backup dos arquivos originais
2. **Validação**: Revise os resultados antes de usar em produção
3. **Encoding**: Para CSVs com acentos, prefira UTF-8
4. **Memória**: Para planilhas muito grandes (>100k linhas), processe em lotes

## 🐛 Solução de Problemas

### Erro de Encoding
```bash
# Se der erro de encoding, tente converter o CSV para UTF-8 primeiro
```

### Colunas não Reconhecidas
- Verifique se os nomes das colunas estão corretos
- O sistema é case-sensitive para alguns nomes

### Números de Telefone Estranhos
- O sistema sempre tenta preservar dados, movendo para anotações quando inválidos
- Revise a coluna "Anotações Gerais" para dados que precisam de atenção manual

## 📞 Suporte

Para dúvidas ou problemas, verifique:
1. Se as dependências estão instaladas
2. Se o arquivo de entrada existe e está acessível
3. Se há permissão de escrita no diretório de saída
