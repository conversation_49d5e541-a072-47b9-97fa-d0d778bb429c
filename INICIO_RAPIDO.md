# ⚡ In<PERSON>cio <PERSON>pido - Formatador de Planilha

## 🎯 Em 3 Passos

### 1️⃣ Instalar
```bash
pip install pandas openpyxl
```

### 2️⃣ Usar
```bash
python formatar.py sua_planilha.xlsx
```

### 3️⃣ Pronto!
Seu arquivo formatado estará salvo como `sua_planilha_formatado.xlsx`

## 📋 O que o Sistema Faz

### ✅ Corrige Automaticamente:
- **Telefones**: Remove caracteres especiais, completa dígitos faltantes
- **Estados**: Converte para siglas UF válidas (SP, RJ, MG...)
- **CEPs**: Formata para 00000-000 com 8 dígitos
- **Dados extras**: Move para coluna "Anotações Gerais"

### 📊 Exemplo Rápido:

**ANTES:**
```
Nome: <PERSON>: (11) 99999-8888, 11987654321
Estado: São Paulo - SP
CEP: 1234567
```

**DEPOIS:**
```
Nome: <PERSON>: 11999998888
Estado: SP
CEP: 12345-670
Anotações Gerais: Contato extra 1: 11987654321 | Estado original: São Paulo - SP
```

## 🚀 Comandos Essenciais

```bash
# Formatar planilha (mais simples)
python formatar.py contatos.xlsx

# Ver demonstração completa
python exemplo_uso.py

# Usar versão avançada
python formatador_planilha.py entrada.xlsx saida.xlsx
```

## 📁 Arquivos Importantes

- **`formatar.py`** ← Use este! (mais simples)
- **`formatador_planilha.py`** ← Versão completa
- **`README.md`** ← Documentação completa
- **`EXEMPLOS.md`** ← Casos práticos

## ⚠️ Importante

1. **Faça backup** dos arquivos originais
2. **Feche o Excel** antes de processar
3. **Revise** a coluna "Anotações Gerais" no resultado

## 🆘 Problemas?

### Erro "python não reconhecido"
- Instale Python de python.org
- Marque "Add to PATH"

### Erro "Permission denied"
- Feche arquivos Excel abertos
- Tente executar como administrador

### Números estranhos
- É normal! Dados problemáticos vão para "Anotações Gerais"
- Revise essa coluna para ajustes manuais

## 🎉 Pronto para Usar!

Agora é só executar:
```bash
python formatar.py sua_planilha.xlsx
```

E seu arquivo será formatado seguindo todas as regras! 🚀
