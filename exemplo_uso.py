#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Exemplo de uso do Formatador de Planilha
"""

import pandas as pd
from formatador_planilha import FormatadorPlanilha


def criar_planilha_exemplo():
    """Cria uma planilha de exemplo com dados para testar"""
    dados_exemplo = [
        {
            'Nome': '<PERSON>',
            'Celular': '(11) 99999-8888, 11987654321',
            'Telefone': '11 3333-4444',
            'País': 'Brasil',
            'Estado': 'São Paulo - SP',
            'Cidade': 'São Paulo',
            'Bairro': 'Centro',
            'Endereço': '<PERSON><PERSON> Flores, 123',
            'CEP': '01234-567',
            'Anotações Gerais': 'Cliente VIP'
        },
        {
            'Nome': '<PERSON>',
            'Celular': '21987654321',
            'Telefone': '2133334444',
            '<PERSON><PERSON>': '',
            'Estado': 'RJ',
            'Cidade': 'Rio de Janeiro',
            '<PERSON>rro': 'Copacabana',
            'Endereço': 'Av. Atlântica, 456',
            'CEP': '22070',
            'Anotações Gerais': ''
        },
        {
            'Nome': 'Pedro Costa',
            'Celular': '85999887766, (85)98765-4321, 85abc123def',
            'Telefone': '8533334444',
            'País': 'Brasil',
            'Estado': 'Ceará',
            'Cidade': 'Fortaleza',
            'Bairro': 'Aldeota',
            'Endereço': 'Rua Barão de Studart, 789',
            'CEP': '60120030',
            'Anotações Gerais': 'Contato preferencial por WhatsApp'
        },
        {
            'Nome': 'Ana Oliveira',
            'Celular': '1199998',  # Número incompleto
            'Telefone': '',
            'País': 'Brasil',
            'Estado': 'MG',
            'Cidade': 'Belo Horizonte',
            'Bairro': 'Savassi',
            'Endereço': 'Rua da Bahia, 321',
            'CEP': '30112000',
            'Anotações Gerais': 'Verificar dados de contato'
        },
        {
            'Nome': 'Carlos Ferreira',
            'Celular': '47999887766',
            'Telefone': '4733334444',
            'País': 'Brasil',
            'Estado': 'Santa Catarina - SC - Sul',
            'Cidade': 'Blumenau',
            'Bairro': 'Victor Konder',
            'Endereço': 'Rua XV de Novembro, 654',
            'CEP': '89010',
            'Anotações Gerais': ''
        }
    ]
    
    df = pd.DataFrame(dados_exemplo)
    df.to_excel('exemplo_contatos.xlsx', index=False)
    print("✅ Planilha de exemplo criada: exemplo_contatos.xlsx")
    return 'exemplo_contatos.xlsx'


def demonstrar_formatacao():
    """Demonstra o uso do formatador"""
    print("🔧 Formatador de Planilha - Demonstração")
    print("=" * 50)
    
    # Criar planilha de exemplo
    arquivo_exemplo = criar_planilha_exemplo()
    
    # Mostrar dados originais
    print("\n📋 Dados originais:")
    df_original = pd.read_excel(arquivo_exemplo)
    print(df_original.to_string(index=False))
    
    # Processar com o formatador
    print("\n🔄 Processando planilha...")
    formatador = FormatadorPlanilha()
    arquivo_formatado = formatador.processar_planilha(arquivo_exemplo)
    
    # Mostrar dados formatados
    print("\n✅ Dados formatados:")
    df_formatado = pd.read_excel(arquivo_formatado)
    print(df_formatado.to_string(index=False))
    
    print(f"\n📁 Arquivos criados:")
    print(f"  - Original: {arquivo_exemplo}")
    print(f"  - Formatado: {arquivo_formatado}")


def testar_regras_individuais():
    """Testa cada regra individualmente"""
    print("\n🧪 Testando regras individuais:")
    print("=" * 40)
    
    formatador = FormatadorPlanilha()
    
    # Teste telefones
    print("\n📱 Teste de formatação de telefones:")
    telefones_teste = [
        "(11) 99999-8888",
        "11999998888",
        "1199999888",  # 10 dígitos
        "119999888",   # 9 dígitos - deve completar
        "11999998888, 11987654321",  # múltiplos
        "11abc999def888",  # com letras
        ""
    ]
    
    for tel in telefones_teste:
        resultado, extras = formatador.formatar_telefone(tel)
        print(f"  '{tel}' → '{resultado}' | Extras: {extras}")
    
    # Teste CEPs
    print("\n📮 Teste de formatação de CEPs:")
    ceps_teste = [
        "01234-567",
        "01234567",
        "1234567",   # 7 dígitos - deve completar
        "123456",    # 6 dígitos - deve completar
        "123456789", # 9 dígitos - deve truncar
        ""
    ]
    
    for cep in ceps_teste:
        resultado = formatador.formatar_cep(cep)
        print(f"  '{cep}' → '{resultado}'")
    
    # Teste estados
    print("\n🗺️  Teste de formatação de estados:")
    estados_teste = [
        "SP",
        "São Paulo",
        "São Paulo - SP",
        "RJ - Rio de Janeiro",
        "Estado inválido",
        "MG",
        ""
    ]
    
    for estado in estados_teste:
        resultado, extras = formatador.formatar_estado(estado)
        print(f"  '{estado}' → '{resultado}' | Extras: {extras}")


if __name__ == "__main__":
    demonstrar_formatacao()
    testar_regras_individuais()
