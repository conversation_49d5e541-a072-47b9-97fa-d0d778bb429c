#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cria arquivo de teste para demonstração
"""

import pandas as pd

# Dados de teste
dados = [
    {
        'Nome': '<PERSON>',
        'Celular': '(11) 99999-8888, 11987654321',
        'Telefone': '11 3333-4444',
        'Estado': 'São Paulo - SP',
        'CEP': '01234567',
        'Anotações Gerais': 'Cliente VIP'
    },
    {
        'Nome': '<PERSON>',
        'Celular': '21987654321',
        'Telefone': '2133334444',
        'Estado': 'RJ',
        'CEP': '22070',
        'Anotações Gerais': ''
    }
]

df = pd.DataFrame(dados)
df.to_excel('teste_contatos.xlsx', index=False)
print("✅ Arquivo teste_contatos.xlsx criado!")
