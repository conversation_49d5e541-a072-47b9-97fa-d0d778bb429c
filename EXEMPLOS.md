# 📋 Exemplos Práticos - Formatador de Planilha

## 🎯 Casos de Uso Reais

### Exemplo 1: Telefones Múltiplos

**Antes:**
```
Nome: <PERSON>: (11) 99999-8888, 11987654321, (11)95555-4444
```

**Depois:**
```
Nome: <PERSON>: 11999998888
Anotações Gerais: Contato extra 1: 11987654321 | Contato extra 2: 11955554444
```

### Exemplo 2: Números Incompletos

**Antes:**
```
Nome: <PERSON>: 11999988
Telefone: 1133334
```

**Depois:**
```
Nome: <PERSON>: 00011999988
Telefone: 00001133334
```

### Exemplo 3: Estados com Texto Extra

**Antes:**
```
Nome: <PERSON>: São Paulo - SP - Região Sudeste
```

**Depois:**
```
Nome: <PERSON>: SP
Anotações Gerais: Estado original: São Paulo - SP - <PERSON><PERSON><PERSON>
```

### Exemplo 4: CEPs Incompletos

**Antes:**
```
Nome: <PERSON>
CEP: 1234567
```

**Depois:**
```
Nome: Ana Oliveira
CEP: 12345-670
```

## 🔧 Comandos Práticos

### Processar Planilha Única
```bash
python formatar.py contatos.xlsx
```

### Processar Múltiplas Planilhas
```bash
# Windows
for %f in (*.xlsx) do python formatar.py "%f"

# Linux/Mac
for file in *.xlsx; do python formatar.py "$file"; done
```

### Processar CSV
```bash
python formatar.py dados.csv
```

## 📊 Estruturas de Planilha Suportadas

### Estrutura Mínima
```
Nome | Celular | Telefone | Estado | CEP
```

### Estrutura Completa
```
Nome | Celular | Telefone | País | Estado | Cidade | Bairro | Endereço | CEP | Anotações Gerais
```

### Variações de Nomes de Colunas
- **Celular**: `Celular`, `celular`, `CELULAR`
- **Telefone**: `Telefone`, `telefone`, `TELEFONE`
- **Estado**: `Estado`, `estado`, `UF`, `uf`
- **CEP**: `CEP`, `cep`
- **Anotações**: `Anotações Gerais`, `Observações`, `Obs`

## 🧪 Casos de Teste

### Teste 1: Dados Problemáticos
```python
# Criar arquivo de teste
import pandas as pd

dados_problematicos = [
    {
        'Nome': 'Teste 1',
        'Celular': '11abc999def888',  # Com letras
        'Estado': 'Estado Inválido',   # Estado inexistente
        'CEP': '123'                   # CEP muito curto
    },
    {
        'Nome': 'Teste 2',
        'Celular': '11999998888, 11987654321, 11955554444',  # Múltiplos
        'Estado': 'Rio de Janeiro - RJ - Sudeste',            # Texto extra
        'CEP': '123456789'                                    # CEP muito longo
    }
]

df = pd.DataFrame(dados_problematicos)
df.to_excel('teste_problematico.xlsx', index=False)
```

### Teste 2: Dados Internacionais
```python
dados_internacionais = [
    {
        'Nome': 'Cliente Internacional',
        'Celular': '+55 11 99999-8888',  # Com código país
        'País': 'Brasil',
        'Estado': 'SP',
        'CEP': '01234-567'
    }
]
```

## 📈 Resultados Esperados

### Telefones
- ✅ `(11) 99999-8888` → `11999998888`
- ✅ `11 99999-8888` → `11999998888`
- ✅ `119999888` → `00119999888` (completa com zeros)
- ✅ `11abc999def888` → `11999888` (remove letras)

### Estados
- ✅ `SP` → `SP`
- ✅ `São Paulo` → `SP` (encontra sigla)
- ✅ `Rio de Janeiro - RJ` → `RJ`
- ❌ `Estado Inválido` → `''` (vai para anotações)

### CEPs
- ✅ `01234567` → `01234-567`
- ✅ `1234567` → `12345-670` (completa)
- ✅ `123456789` → `12345-678` (trunca)

## 🚨 Situações Especiais

### Dados Muito Problemáticos
Se uma linha tem muitos problemas, todos os dados extras vão para "Anotações Gerais":

**Entrada:**
```
Nome: João Problemático
Celular: abc123def, 11999998888, 11987654321, xyz
Estado: Estado Inexistente - Região Fantasma
```

**Saída:**
```
Nome: João Problemático
Celular: 11999998888
Estado: 
Anotações Gerais: Contato inválido 1: abc123def | Contato extra 1: 11987654321 | Contato inválido 3: xyz | Estado inválido: Estado Inexistente - Região Fantasma
```

### Planilhas Grandes
Para planilhas com mais de 10.000 linhas:
- O processamento pode demorar alguns minutos
- Monitore o uso de memória
- Considere dividir em arquivos menores

### Encoding de CSV
Se tiver problemas com acentos:
1. Abra o CSV no Excel
2. Salve como "CSV UTF-8"
3. Ou use planilhas .xlsx que são mais confiáveis

## 💡 Dicas de Uso

1. **Sempre faça backup** dos arquivos originais
2. **Teste com poucos dados** primeiro
3. **Revise a coluna "Anotações Gerais"** para dados que precisam atenção manual
4. **Use .xlsx em vez de .csv** quando possível
5. **Feche arquivos Excel** antes de processar
